import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Text, View } from 'react-native';
import AppNavigator from './src/navigation/AppNavigator';
import { CartProvider } from './src/contexts/CartContext';
import ErrorBoundary from './src/components/ErrorBoundary';
import { validateEnvironment } from './src/config/environment';

// Import CSS conditionally to prevent production crashes
try {
  require('./global.css');
} catch (error) {
  console.warn('NativeWind CSS not loaded:', error);
}

// Validate environment variables on app startup
console.log('🔧 Validating environment configuration...');
const envValid = validateEnvironment();
if (!envValid) {
  console.error('❌ Environment validation failed - app may not function correctly');
} else {
  console.log('✅ Environment validation passed');
}

export default function App() {
  return (
    <ErrorBoundary>
      <SafeAreaProvider>
        <CartProvider>
          <NavigationContainer
            fallback={
              <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <Text>Loading...</Text>
              </View>
            }
          >
            <AppNavigator />
            <StatusBar style="light" />
          </NavigationContainer>
        </CartProvider>
      </SafeAreaProvider>
    </ErrorBoundary>
  );
}
