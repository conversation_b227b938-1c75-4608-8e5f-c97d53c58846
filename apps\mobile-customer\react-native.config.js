const path = require('path');
const fs = require('fs');

// Function to find expo-modules-core in pnpm structure
function findExpoModulesCore() {
  const searchPaths = [
    // Root node_modules
    path.resolve(__dirname, '../../node_modules/expo-modules-core'),
    // Root pnpm structure
    path.resolve(__dirname, '../../node_modules/.pnpm'),
    // Local pnpm structure
    path.resolve(__dirname, './node_modules/.pnpm'),
  ];

  for (const searchPath of searchPaths) {
    if (searchPath.includes('.pnpm')) {
      // Search in pnpm structure
      if (fs.existsSync(searchPath)) {
        const pnpmDirs = fs.readdirSync(searchPath);
        for (const dir of pnpmDirs) {
          if (dir.startsWith('expo-modules-core@')) {
            const expoModulesPath = path.join(searchPath, dir, 'node_modules', 'expo-modules-core');
            if (fs.existsSync(expoModulesPath)) {
              console.log(`🔍 Found expo-modules-core in pnpm structure: ${expoModulesPath}`);
              return expoModulesPath;
            }
          }
        }
      }
    } else {
      // Direct path check
      if (fs.existsSync(searchPath)) {
        console.log(`🔍 Found expo-modules-core in direct path: ${searchPath}`);
        return searchPath;
      }
    }
  }

  console.log('⚠️  expo-modules-core not found in any search path');
  return null;
}

const expoModulesCoreRoot = findExpoModulesCore();

const config = {
  project: {
    android: {
      sourceDir: './android',
      appName: 'app',
      packageName: 'com.tap2go.mobile',
    },
  },
};

// Only add dependencies configuration if expo-modules-core is found
if (expoModulesCoreRoot) {
  const androidSourceDir = path.join(expoModulesCoreRoot, 'android');

  if (fs.existsSync(androidSourceDir)) {
    console.log(`✅ Adding expo-modules-core Android autolinking: ${androidSourceDir}`);

    config.dependencies = {
      'expo-modules-core': {
        platforms: {
          android: {
            sourceDir: androidSourceDir,
            packageImportPath: 'import expo.core.ExpoModulesPackage;',
          },
        },
      },
    };
  } else {
    console.log(`⚠️  expo-modules-core Android source directory not found: ${androidSourceDir}`);
  }
} else {
  console.log('⚠️  Skipping expo-modules-core autolinking configuration - module not found');
}

module.exports = config;
