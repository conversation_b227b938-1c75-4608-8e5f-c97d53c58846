module.exports = {
  project: {
    android: {
      sourceDir: './android',
      appName: 'app',
      packageName: 'com.tap2go.mobile',
    },
  },
  dependencies: {
    // Disable React Native CLI autolinking for expo-modules-core
    // Let Expo's own autolinking system handle it
    'expo-modules-core': {
      platforms: {
        android: {
          sourceDir: null, // disable Android platform auto linking
          packageImportPath: null,
        },
      },
    },
  },
};
