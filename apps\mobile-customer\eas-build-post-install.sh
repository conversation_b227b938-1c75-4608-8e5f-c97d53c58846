#!/bin/bash

# EAS Build Post-Install Hook for Tap2Go Mobile Customer
# This script runs after pnpm install in EAS Build environment

echo "🔧 EAS Build Post-Install Hook - Tap2Go Mobile Customer"
echo "📦 Verifying dependency resolution..."

# Check if we're in EAS Build environment
if [ "$EAS_BUILD" = "true" ] || [ "$CI" = "true" ]; then
    echo "✅ EAS Build environment detected"
    
    # Create symlinks for expo-modules-core to ensure Metro can find it
    echo "🔗 Creating expo-modules-core symlinks for Metro resolution..."

    # Create local node_modules if it doesn't exist
    mkdir -p node_modules

    # Remove existing symlink if it exists
    if [ -L "node_modules/expo-modules-core" ] || [ -d "node_modules/expo-modules-core" ]; then
        rm -rf node_modules/expo-modules-core
        echo "🗑️ Removed existing expo-modules-core"
    fi

    # Create symlink from root node_modules to local node_modules
    if [ -d "../../node_modules/expo-modules-core" ]; then
        ln -sf "$(pwd)/../../node_modules/expo-modules-core" "node_modules/expo-modules-core"
        echo "✅ Created symlink: node_modules/expo-modules-core -> ../../node_modules/expo-modules-core"

        # Verify the symlink works
        if [ -f "node_modules/expo-modules-core/package.json" ]; then
            echo "✅ expo-modules-core symlink verified - package.json accessible"
        else
            echo "❌ expo-modules-core symlink failed - package.json not accessible"
            exit 1
        fi
    else
        echo "❌ expo-modules-core NOT found in root node_modules"
        exit 1
    fi
    
    # Also create symlink for expo to ensure consistency
    if [ -L "node_modules/expo" ] || [ -d "node_modules/expo" ]; then
        rm -rf node_modules/expo
        echo "🗑️ Removed existing expo"
    fi

    if [ -d "../../node_modules/expo" ]; then
        ln -sf "$(pwd)/../../node_modules/expo" "node_modules/expo"
        echo "✅ Created symlink: node_modules/expo -> ../../node_modules/expo"

        # Verify the expo symlink works
        if [ -f "node_modules/expo/package.json" ]; then
            echo "✅ expo symlink verified - package.json accessible"
        else
            echo "❌ expo symlink failed - package.json not accessible"
            exit 1
        fi
    else
        echo "❌ expo NOT found in root node_modules"
        exit 1
    fi
    
    # Verify Metro runtime
    echo "🔍 Verifying @expo/metro-runtime..."
    if [ -d "../../node_modules/@expo/metro-runtime" ]; then
        echo "✅ @expo/metro-runtime found in root node_modules"
    else
        echo "❌ @expo/metro-runtime NOT found in root node_modules"
        exit 1
    fi

    # Verify scheduler module
    echo "🔍 Verifying scheduler..."
    if [ -d "../../node_modules/scheduler" ]; then
        echo "✅ scheduler found in root node_modules"
        if [ -f "../../node_modules/scheduler/index.native.js" ]; then
            echo "✅ scheduler/index.native.js exists"
        else
            echo "❌ scheduler/index.native.js NOT found"
            exit 1
        fi
    else
        echo "❌ scheduler NOT found in root node_modules"
        exit 1
    fi
    
    # Test Metro config loading
    echo "🔧 Testing Metro configuration..."
    if node -e "require('./metro.config.eas.js'); console.log('✅ EAS Metro config loads successfully')"; then
        echo "✅ EAS Metro configuration is valid"
    else
        echo "❌ EAS Metro configuration has errors"
        exit 1
    fi

    # Test scheduler resolution specifically
    echo "🔧 Testing scheduler module resolution..."
    if node -e "
        const path = require('path');
        const fs = require('fs');
        const schedulerPath = path.resolve('../../node_modules/scheduler/index.native.js');
        if (fs.existsSync(schedulerPath)) {
            const scheduler = require(schedulerPath);
            if (typeof scheduler.unstable_scheduleCallback === 'function') {
                console.log('✅ Scheduler module resolution successful');
            } else {
                console.log('❌ Scheduler missing expected exports');
                process.exit(1);
            }
        } else {
            console.log('❌ Scheduler native file not found');
            process.exit(1);
        }
    "; then
        echo "✅ Scheduler module verification passed"
    else
        echo "❌ Scheduler module verification failed"
        exit 1
    fi
    
    echo "✅ All dependency verifications passed"
else
    echo "⚠️  Not in EAS Build environment, skipping verifications"
fi

echo "🎯 Post-install hook completed successfully"
