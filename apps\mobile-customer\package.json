{"name": "mobile-customer", "version": "1.0.0", "main": "index.ts", "scripts": {"dev": "node start-enterprise.js", "start": "node start-enterprise.js", "android": "node start-enterprise.js --android", "ios": "node start-enterprise.js --ios", "web": "node start-enterprise.js --web", "start-enterprise": "node start-enterprise.js", "start-safe": "node fix-expo-cache.js", "start-fallback": "copy metro.config.fallback.js metro.config.js && node fix-expo-cache.js", "start-original": "npx expo start", "cache:validate": "node scripts/enterprise-cache-manager.js validate", "cache:clean": "node scripts/enterprise-cache-manager.js clean", "cache:optimize": "node scripts/enterprise-cache-manager.js optimize", "cache:reset": "node scripts/enterprise-cache-manager.js full-reset", "cache:legacy-clean": "node scripts/metro-cache-manager.js clean", "cache:legacy-validate": "node scripts/metro-cache-manager.js validate", "cache:legacy-fix-deps": "node scripts/metro-cache-manager.js fix-deps", "clear-cache": "pnpm run cache:clean && pnpm start", "reset-metro": "pnpm run cache:reset && pnpm start", "type-check": "tsc --noEmit", "build:android:apk": "npx expo prebuild --platform android && cd android && ./gradlew assembleRelease", "build:android:aab": "npx expo prebuild --platform android && cd android && ./gradlew bundleRelease", "build:android:debug": "npx expo prebuild --platform android && cd android && ./gradlew assembleDebug", "prebuild:android": "npx expo prebuild --platform android --clean", "prebuild:clean": "npx expo prebuild --clear", "postinstall": "bash eas-build-post-install.sh || echo 'Post-install script skipped (not in EAS build environment)'"}, "dependencies": {"@babel/plugin-transform-runtime": "^7.27.4", "@babel/runtime": "^7.27.6", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-firebase/app": "^22.2.1", "@react-native-firebase/auth": "^22.2.1", "@react-native-firebase/firestore": "^22.2.1", "@react-native/assets-registry": "^0.79.4", "@react-native/normalize-colors": "^0.79.4", "@react-native/virtualized-lists": "^0.79.4", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/core": "^7.1.6", "@react-navigation/elements": "^2.1.14", "@react-navigation/native": "^7.1.6", "@react-navigation/routers": "^7.1.14", "@react-navigation/stack": "^7.3.6", "abort-controller": "^3.0.0", "ansi-regex": "^6.1.0", "base64-js": "^1.5.1", "buffer": "^6.0.3", "color": "^4.2.3", "color-convert": "^3.1.0", "color-name": "^2.0.0", "color-string": "^1.9.1", "css-mediaquery": "^0.1.2", "decode-uri-component": "^0.4.1", "escape-string-regexp": "^5.0.0", "event-target-shim": "^5.0.0", "expo": "53.0.13", "expo-asset": "^11.1.5", "expo-build-properties": "~0.14.6", "expo-constants": "^17.1.6", "expo-dev-client": "^5.2.2", "expo-file-system": "^18.1.10", "expo-font": "^13.3.1", "expo-linear-gradient": "~14.1.5", "expo-linking": "^7.1.5", "expo-modules-core": "2.4.0", "expo-router": "~5.1.1", "expo-status-bar": "~2.2.3", "fbjs": "^3.0.5", "filter-obj": "^6.1.0", "firebase": "^11.8.1", "hoist-non-react-statics": "^3.3.2", "ieee754": "^1.2.1", "invariant": "^2.2.4", "is-arrayish": "^0.3.2", "memoize-one": "^6.0.0", "nanoid": "^5.0.9", "nativewind": "^2.0.11", "nullthrows": "^1.1.1", "promise": "^8.3.0", "query-string": "^9.1.1", "react": "19.0.0", "react-dom": "19.0.0", "react-freeze": "^1.0.0", "react-is": "^18.3.1", "react-native": "0.79.4", "react-native-edge-to-edge": "^1.6.0", "react-native-gesture-handler": "~2.24.0", "react-native-is-edge-to-edge": "^1.1.4", "react-native-maps": "1.20.1", "react-native-reanimated": "3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.11.1", "react-native-svg": "15.11.2", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "react-native-web-linear-gradient": "^1.1.2", "regenerator-runtime": "^0.14.1", "scheduler": "^0.25.0", "simple-swizzle": "^0.2.2", "split-on-first": "^3.0.0", "stacktrace-parser": "^0.1.10", "typescript": "~5.8.3", "use-latest-callback": "^0.2.1", "use-sync-external-store": "^1.2.0", "warn-once": "^0.1.1", "webidl-conversions": "^7.0.0", "whatwg-fetch": "^3.0.0", "whatwg-url-without-unicode": "^8.0.0-3"}, "devDependencies": {"@babel/core": "^7.27.4", "@expo/cli": "^0.24.15", "@react-native-community/cli": "^18.0.0", "@types/react": "~19.0.10", "babel-preset-expo": "~13.0.0", "prettier-plugin-tailwindcss": "^0.5.14", "tailwindcss": "3.3.2", "typescript": "~5.8.3"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false, "exclude": ["react-native-is-edge-to-edge"]}}, "autolinking": {"android": {"packageName": "com.tap2go.mobile"}}}, "private": true}